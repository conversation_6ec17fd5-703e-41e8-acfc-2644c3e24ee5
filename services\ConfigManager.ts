/**
 * 🔧 配置管理器服務
 * 
 * 負責載入和管理所有遊戲配置數據
 * 對應 docs/game_design.md 中的配置系統設計
 */

import * as FileSystem from 'expo-file-system';
import { Asset } from 'expo-asset';
import {
  ConfigManager,
  CardConfig,
  SkillConfig,
  StageConfig,
  EnemyDeckConfig,
  DropConfig,
  GachaConfig,
  AIBehaviorConfig,
  PlayerTeamConfig,
  CSVParseResult,
  ConfigValidationResult
} from '../types/config';

/**
 * 配置管理器實現
 */
export class GameConfigManager implements ConfigManager {
  // 配置數據存儲
  cards = new Map<string, CardConfig>();
  skills = new Map<string, SkillConfig>();
  stages = new Map<string, StageConfig>();
  enemyDecks = new Map<string, EnemyDeckConfig>();
  drops = new Map<string, DropConfig>();
  gacha = new Map<string, GachaConfig>();
  aiBehaviors = new Map<string, AIBehaviorConfig>();
  playerTeams = new Map<string, PlayerTeamConfig>();

  private isLoaded = false;
  private loadingPromise: Promise<void> | null = null;

  /**
   * 載入所有配置文件
   */
  async loadConfigs(): Promise<void> {
    if (this.isLoaded) {
      return;
    }

    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingPromise = this._loadConfigsInternal();
    return this.loadingPromise;
  }

  private async _loadConfigsInternal(): Promise<void> {
    try {
      console.log('🔧 開始載入遊戲配置...');
      console.log('🔧 ConfigManager: _loadConfigsInternal 被調用');

      // 並行載入所有配置文件
      const [
        cardsResult,
        skillsResult,
        stagesResult,
        enemyDecksResult,
        dropsResult,
        gachaResult,
        aiBehaviorsResult,
        playerTeamsResult
      ] = await Promise.all([
        this.loadCSVConfig<CardConfig>('CardConfig.csv'),
        this.loadCSVConfig<SkillConfig>('SkillConfig.csv'),
        this.loadCSVConfig<StageConfig>('StageConfig.csv'),
        this.loadCSVConfig<EnemyDeckConfig>('EnemyDeckConfig.csv'),
        this.loadCSVConfig<DropConfig>('DropConfig.csv'),
        this.loadCSVConfig<GachaConfig>('GachaConfig.csv'),
        this.loadCSVConfig<AIBehaviorConfig>('AIBehaviorConfig.csv'),
        this.loadCSVConfig<PlayerTeamConfig>('PlayerTeamConfig.csv')
      ]);

      // 處理載入結果
      this.processConfigResults('cards', cardsResult, this.cards);
      this.processConfigResults('skills', skillsResult, this.skills);
      this.processConfigResults('stages', stagesResult, this.stages);
      this.processConfigResults('enemyDecks', enemyDecksResult, this.enemyDecks);
      this.processConfigResults('drops', dropsResult, this.drops);
      this.processConfigResults('gacha', gachaResult, this.gacha);
      this.processConfigResults('aiBehaviors', aiBehaviorsResult, this.aiBehaviors);
      this.processConfigResults('playerTeams', playerTeamsResult, this.playerTeams);

      // 驗證配置完整性
      const validationResult = this.validateConfigs();
      if (!validationResult.isValid) {
        console.warn('⚠️ 配置驗證發現問題:', validationResult.errors);
      }

      this.isLoaded = true;
      console.log('✅ 配置載入完成');
      console.log(`📊 載入統計: 卡牌${this.cards.size}, 技能${this.skills.size}, 關卡${this.stages.size}`);

    } catch (error) {
      console.error('❌ 配置載入失敗:', error);
      throw new Error(`配置載入失敗: ${error}`);
    }
  }

  /**
   * 載入單個 CSV 配置文件
   */
  private async loadCSVConfig<T>(filename: string): Promise<CSVParseResult<T>> {
    try {
      // 由於 Metro bundler 不支持動態 require，我們直接使用默認數據
      console.log(`📁 載入配置文件: ${filename}`);

      // 使用默認配置數據
      const csvContent = this.getDefaultCSVContent(filename);

      // 解析 CSV
      const parseResult = this.parseCSV<T>(csvContent, filename);

      return parseResult;

    } catch (error) {
      console.error(`❌ 載入 ${filename} 失敗:`, error);

      // 返回默認數據
      const defaultContent = this.getDefaultCSVContent(filename);
      return this.parseCSV<T>(defaultContent, filename);
    }
  }

  /**
   * 獲取默認 CSV 內容（用於測試）
   */
  private getDefaultCSVContent(filename: string): string {
    switch (filename) {
      case 'CardConfig.csv':
        return `id,name,race,rarity,baseAttack,baseMagicAttack,baseDefense,baseCritRate,baseHealth,baseSpeed,tags,skillIds,imageUrl,description,attackGrowth,magicAttackGrowth,defenseGrowth,critRateGrowth,healthGrowth,speedGrowth,maxLevel,evolutionCardId,evolutionRequiredLevel,evolutionRequiredCards
CARD_001,人族戰士,Human,2,120,50,80,0.05,300,15,Warrior;Fire,SKILL_001;SKILL_003,warrior_human.png,勇敢的人族戰士，擅長近戰攻擊,8,2,5,0.002,20,1,60,,0,0
CARD_002,精靈法師,Elf,3,60,150,40,0.08,200,20,Mage;Light,SKILL_002,mage_elf.png,智慧的精靈法師，精通光系魔法,3,12,3,0.003,15,2,80,,0,0
CARD_003,獸人小兵,Orc,1,100,30,60,0.03,250,12,Warrior;Minion,SKILL_001,orc_minion.png,普通的獸人士兵，力量強大但速度較慢,6,1,4,0.001,18,0.5,50,,0,0
CARD_004,龍族首領,Dragon,4,200,180,120,0.12,500,18,Boss;Elite;Fire,SKILL_004,dragon_boss.png,強大的龍族首領，擁有毀滅性的龍息攻擊,15,15,8,0.004,35,2,90,,0,0`;

      case 'StageConfig.csv':
        return `id,name,description,stageNumber,backgroundImageUrl,musicId,previousStageId,rewardGold,firstClearRewardId,recommendedPower,loopMonsterPoolDeckId,loopMinEnemiesPerBattle,loopMaxEnemiesPerBattle,loopDropPoolId,bossDeckId,bossDropPoolId
STAGE_001,火焰洞穴,充滿獸人和龍族的危險洞穴,1,cave_fire.jpg,battle_theme_01,,100,REWARD_001,1500,DECK_001,1,1,DROP_001,DECK_002,DROP_002`;

      case 'SkillConfig.csv':
        return `id,name,description,targetType,effectType,baseValue,valueGrowth,cooldown,animationId,soundId,particleEffectId,statusEffectId,statusEffectDuration,isPassive,triggerCondition,priority
SKILL_001,普通攻擊,基礎的物理攻擊,SingleEnemy,Damage,100,5,0,attack_slash,sword_hit,none,,0,false,,1
SKILL_002,火球術,發射火球造成魔法傷害,SingleEnemy,Damage,120,8,3,fireball_cast,fire_magic,fire_explosion,,0,false,,2
SKILL_003,重擊,強力的物理攻擊，冷卻時間較長,SingleEnemy,Damage,180,10,5,heavy_strike,heavy_hit,impact_burst,,0,false,,3
SKILL_004,龍息,龍族的毀滅性攻擊，對所有敵人造成傷害,AllEnemies,Damage,150,12,8,dragon_breath,dragon_roar,fire_breath,,0,false,,4`;

      case 'PlayerTeamConfig.csv':
        return `id,name,cardIds,cardLevels,formation,description
PLAYER_TEAM_001,初始隊伍,CARD_001;CARD_002;CARD_003,5;3;4,4,新手起始隊伍`;

      case 'EnemyDeckConfig.csv':
        return `id,name,cardIds,cardLevels,aiBehaviorType,totalPower,formation,specialRules
DECK_001,獸人小兵組,CARD_003,10,Aggressive,1000,4,隨機出現的獸人小兵
DECK_002,龍族首領組,CARD_004,15,Balanced,3000,4,強大的龍族首領Boss`;

      case 'DropConfig.csv':
        return `id,name,description,maxDropCount,minDropCount
DROP_001,小兵掉落池,獸人小兵的基礎掉落,2,1
DROP_002,Boss掉落池,龍族首領的豐富掉落,4,2`;

      case 'GachaConfig.csv':
        return `id,name,description,isActive,startTime,endTime,singleDrawCost,tenDrawCost,currencyType,bannerImageUrl,featuredCardIds
GACHA_001,基礎卡包,包含基礎卡牌的抽卡池,true,,,100,900,gold,basic_banner.jpg,CARD_001;CARD_002`;

      case 'AIBehaviorConfig.csv':
        return `id,name,behaviorType,defensiveThreshold,aggressiveThreshold,retreatThreshold,randomnessFactor
AI_001,攻擊型AI,Aggressive,0.3,0.8,0.1,0.2
AI_002,平衡型AI,Balanced,0.5,0.6,0.2,0.3`;

      default:
        return `id,name
DEFAULT_001,默認項目`;
    }
  }

  /**
   * 解析 CSV 內容
   */
  private parseCSV<T>(csvContent: string, filename: string): CSVParseResult<T> {
    try {
      const lines = csvContent.split('\n').filter(line => line.trim());
      if (lines.length < 2) {
        return {
          success: false,
          data: [],
          errors: [`${filename} 格式錯誤: 至少需要標題行和一行數據`],
          warnings: []
        };
      }

      // 解析標題行
      const headers = lines[0].split(',').map(h => h.trim());
      const data: T[] = [];
      const errors: string[] = [];
      const warnings: string[] = [];

      // 解析數據行
      for (let i = 1; i < lines.length; i++) {
        try {
          const values = lines[i].split(',').map(v => v.trim());
          const row: any = {};

          headers.forEach((header, index) => {
            const value = values[index] || '';
            row[header] = this.parseValue(value, header);
          });

          data.push(row as T);
        } catch (error) {
          errors.push(`第 ${i + 1} 行解析錯誤: ${error}`);
        }
      }

      return {
        success: errors.length === 0,
        data,
        errors,
        warnings
      };

    } catch (error) {
      return {
        success: false,
        data: [],
        errors: [`CSV 解析失敗: ${error}`],
        warnings: []
      };
    }
  }

  /**
   * 解析單個值
   */
  private parseValue(value: string, fieldName: string): any {
    if (!value) return '';

    // 數字類型 - 擴展匹配規則
    if (fieldName.includes('attack') || fieldName.includes('health') ||
        fieldName.includes('speed') || fieldName.includes('level') ||
        fieldName.includes('cost') || fieldName.includes('rate') ||
        fieldName.includes('count') || fieldName.includes('power') ||
        fieldName.includes('Number') || fieldName.includes('Growth') ||
        fieldName.includes('Threshold') || fieldName.includes('Factor') ||
        fieldName.includes('rarity') || fieldName.includes('cooldown') ||
        fieldName.includes('Value') || fieldName.includes('Duration') ||
        fieldName.includes('priority') || fieldName.includes('Gold') ||
        fieldName.includes('Enemies') || fieldName.includes('Drop')) {
      const num = parseFloat(value);
      return isNaN(num) ? 0 : num;
    }

    // 布林類型
    if (fieldName.includes('is') || fieldName.includes('active') || fieldName.includes('passive')) {
      return value.toLowerCase() === 'true' || value === '1';
    }

    // 陣列類型 (用分號分隔)
    if (fieldName.includes('ids') || fieldName.includes('tags') || fieldName.includes('race') ||
        fieldName.includes('Levels') || fieldName.includes('levels')) {
      return value ? value.split(';').map(v => v.trim()).filter(v => v) : [];
    }

    // 字符串類型
    return value;
  }

  /**
   * 處理配置載入結果
   */
  private processConfigResults<T extends { id: string }>(
    configType: string,
    result: CSVParseResult<T>,
    targetMap: Map<string, T>
  ): void {
    if (!result.success) {
      console.error(`❌ ${configType} 配置載入失敗:`, result.errors);
      return;
    }

    result.data.forEach(item => {
      // 特殊處理 StageConfig：將平面字段轉換為嵌套結構
      if (configType === 'stages') {
        const stageItem = item as any;
        const transformedStage = {
          ...stageItem,
          parts: {
            loop: {
              monsterPoolDeckId: stageItem.loopMonsterPoolDeckId,
              minEnemiesPerBattle: stageItem.loopMinEnemiesPerBattle,
              maxEnemiesPerBattle: stageItem.loopMaxEnemiesPerBattle,
              dropPoolId: stageItem.loopDropPoolId,
            },
            boss: {
              bossDeckId: stageItem.bossDeckId,
              dropPoolId: stageItem.bossDropPoolId,
            }
          }
        };

        // 移除原始的平面字段
        delete transformedStage.loopMonsterPoolDeckId;
        delete transformedStage.loopMinEnemiesPerBattle;
        delete transformedStage.loopMaxEnemiesPerBattle;
        delete transformedStage.loopDropPoolId;
        delete transformedStage.bossDeckId;
        delete transformedStage.bossDropPoolId;

        targetMap.set(transformedStage.id, transformedStage as T);
      } else {
        targetMap.set(item.id, item);
      }
    });

    if (result.warnings.length > 0) {
      console.warn(`⚠️ ${configType} 配置警告:`, result.warnings);
    }

    console.log(`✅ ${configType} 配置載入成功: ${result.data.length} 項`);
  }

  /**
   * 驗證配置完整性
   */
  private validateConfigs(): ConfigValidationResult {
    const errors: any[] = [];
    const warnings: any[] = [];

    // 這裡可以添加更多驗證邏輯
    // 例如檢查引用完整性、數值範圍等

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // 配置獲取方法
  getCardConfig(id: string): CardConfig | undefined {
    return this.cards.get(id);
  }

  getSkillConfig(id: string): SkillConfig | undefined {
    return this.skills.get(id);
  }

  getStageConfig(id: string): StageConfig | undefined {
    return this.stages.get(id);
  }

  getEnemyDeckConfig(id: string): EnemyDeckConfig | undefined {
    return this.enemyDecks.get(id);
  }

  getDropConfig(id: string): DropConfig | undefined {
    return this.drops.get(id);
  }

  getGachaConfig(id: string): GachaConfig | undefined {
    return this.gacha.get(id);
  }

  getAIBehaviorConfig(id: string): AIBehaviorConfig | undefined {
    return this.aiBehaviors.get(id);
  }

  getPlayerTeamConfig(id: string): PlayerTeamConfig | undefined {
    return this.playerTeams.get(id);
  }

  /**
   * 獲取所有配置的統計信息
   */
  getConfigStats() {
    return {
      cards: this.cards.size,
      skills: this.skills.size,
      stages: this.stages.size,
      enemyDecks: this.enemyDecks.size,
      drops: this.drops.size,
      gacha: this.gacha.size,
      aiBehaviors: this.aiBehaviors.size,
      playerTeams: this.playerTeams.size,
      isLoaded: this.isLoaded
    };
  }
}

// 全局配置管理器實例
export const configManager = new GameConfigManager();
